#!/usr/bin/env python3
"""
Debug script to see what fields are available in Mindee custom API responses
"""
import os
import sys
import json
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, '/app')

from services.mindee_service import MindeeService

def debug_mindee_fields():
    """Debug what fields are available in the Mindee response"""
    
    # Initialize the service
    mindee_service = MindeeService()
    
    if not mindee_service.api_key:
        print("❌ No Mindee API key found!")
        return
    
    print(f"✅ Mindee API key found: {mindee_service.api_key[:10]}...")
    
    # Find a test image in uploads
    uploads_dir = Path('/app/uploads')
    test_images = list(uploads_dir.glob('*.jpeg')) + list(uploads_dir.glob('*.jpg')) + list(uploads_dir.glob('*.png'))
    
    if not test_images:
        print("❌ No test images found in uploads directory")
        return
    
    test_image = test_images[0]
    print(f"📸 Using test image: {test_image}")
    
    # Test Kast API to see field structure
    print(f"\n🔍 Testing Kast API for field structure...")
    print("=" * 60)
    
    try:
        # Temporarily modify the parsing to show all available fields
        result = mindee_service.process_invoice(str(test_image), 'kast')
        
        print(f"Result keys: {list(result.keys())}")
        print(f"Success: {result.get('success')}")
        print(f"Invoice Number: {result.get('invoice_number')}")
        print(f"Date: {result.get('date')}")
        print(f"Total Amount: {result.get('total_amount')}")
        print(f"Items count: {len(result.get('items', []))}")
        
        if result.get('items'):
            print(f"First item: {result['items'][0]}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    debug_mindee_fields()
